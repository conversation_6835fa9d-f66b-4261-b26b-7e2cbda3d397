import {
  DbModelGenerationTask,
  ModelGenerationTaskType,
} from "@nilo/firebase-schema";
import { generateObject } from "ai";
import { z } from "zod";
import { Logger } from "../../logger";
import { getTracedLLMModel } from "../llmUtils";

/**
 * Moderate task content on the server side as a safety net
 * @param task The model generation task to moderate
 * @param logger Logger instance
 * @returns Promise<{ allowed: boolean; reason?: string }>
 */
export async function moderateTaskContent(
  task: DbModelGenerationTask,
  logger: Logger
): Promise<{ allowed: boolean; reason?: string }> {
  logger.debug("🛡️ Server-side moderation check", {
    taskType: task.taskBody.type,
  });

  switch (task.taskBody.type) {
    case ModelGenerationTaskType.text_to_model: {
      const prompt = task.taskBody.prompt;
      if (!prompt || prompt.trim().length === 0) {
        return { allowed: false, reason: "Empty prompt" };
      }

      // Check with custom OpenAI moderation using GPT-5-mini
      const moderationResult = await checkCustomPromptModeration(
        prompt,
        logger
      );
      if (moderationResult.flagged) {
        return {
          allowed: false,
          reason:
            moderationResult.reason ||
            "Content flagged as inappropriate for 3D model generation",
        };
      }

      break;
    }
    case ModelGenerationTaskType.image_to_model: {
      const imageUrl = task.taskBody.imageUrl;
      if (!imageUrl) {
        return { allowed: false, reason: "No image URL provided" };
      }

      // Check image with custom moderation (analyze image description/context)
      const moderationResult = await checkCustomImageModeration(
        imageUrl,
        logger
      );
      if (moderationResult.flagged) {
        return {
          allowed: false,
          reason:
            moderationResult.reason ||
            "Image flagged as inappropriate for 3D model generation",
        };
      }

      break;
    }
    case ModelGenerationTaskType.animate_rig: {
      // Animation/rigging tasks don't need content moderation
      // The original content was already moderated
      break;
    }
  }

  logger.debug("✅ Server-side moderation passed");
  return { allowed: true };
}

// Zod schema for moderation assessment
const ModerationAssessmentSchema = z.object({
  reason: z
    .string()
    .describe(
      "Explanation for why content was flagged (required if flagged is true)"
    ),
  flagged: z
    .boolean()
    .describe(
      "Whether the content should be blocked or meets the moderation criteria"
    ),
});

/**
 * Custom moderation using GPT-4o-mini for 3D model generation context
 */
async function checkCustomPromptModeration(
  prompt: string,
  logger: Logger
): Promise<{ flagged: boolean; reason?: string }> {
  const model = getTracedLLMModel({
    provider: "openai",
    model: "gpt-5-mini",
    settings: {},
    userId: "system-moderation", // Use a system user ID for moderation
  });

  const { object: assessment } = await generateObject({
    model,
    schema: ModerationAssessmentSchema,
    temperature: 1,
    system:
      "You are a content moderation assistant for a family-friendly 3D modeling platform. Be strict about content that could be inappropriate for children or in educational settings.",
    prompt: `Please assess the following content for appropriateness in a 3D model generation context for a family-friendly creative platform. This platform is used by people of all ages, including children, to create 3D models and objects.

Your assessment should flag content that is inappropriate for this context, including but not limited to:
- Sexual content, anatomy, or suggestive material (including clinical/medical terms like "penis", "vagina", "breast", etc.)
- Violence, weapons, or dangerous items
- Hate speech, harassment, or discriminatory content
- Drug-related content
- Graphic or disturbing imagery
- Any content that would be inappropriate for children or in a family setting

Exceptions
- IP infringement concerns (we do not moderate for copyright issues)
- Well-known names, characters, or proper nouns (e.g., "Dick Grayson", celebrity names, fictional characters)
- Historical figures or events

Context: This is for generating 3D models that will be visible to users of all ages in a creative/educational environment.

Content to assess: "${prompt}"

Provide your assessment with a boolean flagged field and a reason field. If flagged is true, provide a detailed explanation. If flagged is false, provide a brief confirmation like "Prompt to generate 3D cat model is appropriate".`,
  });

  if (assessment.flagged) {
    logger.warn("🚨 Content flagged by moderation", {
      prompt,
      reason: assessment.reason,
    });
  }

  return {
    flagged: assessment.flagged,
    reason: assessment.reason,
  };
}

/**
 * Custom image moderation using GPT-4V for 3D model generation context
 */
async function checkCustomImageModeration(
  imageUrl: string,
  logger: Logger
): Promise<{ flagged: boolean; reason?: string }> {
  logger.debug("🖼️ Processing image for moderation", { imageUrl });

  // Convert image URL to base64 data URL if needed
  const imageData = await ensureBase64DataUrl(imageUrl, logger);

  const model = getTracedLLMModel({
    provider: "openai",
    model: "gpt-5-mini",
    settings: {},
    userId: "system-moderation", // Use a system user ID for moderation
  });

  const { object: assessment } = await generateObject({
    model,
    schema: ModerationAssessmentSchema,
    temperature: 1,
    system:
      "You are a content moderation assistant for a family-friendly 3D modeling platform. Be strict about visual content that could be inappropriate for children or in educational settings.",
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: `Please analyze this image for appropriateness in a 3D model generation context for a family-friendly creative platform. This platform is used by people of all ages, including children, to create 3D models and objects.

Your assessment should flag content that is inappropriate for this context, including but not limited to:
- Sexual content, nudity, or suggestive material
- Violence, weapons, or dangerous items
- Hate symbols or discriminatory imagery
- Drug-related content
- Graphic or disturbing imagery
- Any content that would be inappropriate for children or in a family setting

Context: This image will be used to generate a 3D model that will be visible to users of all ages in a creative/educational environment.

Provide your assessment with a boolean flagged field and a reason field. If flagged is true, provide a detailed explanation. If flagged is false, provide a brief confirmation like "Image of 3D cat model is appropriate".`,
          },
          {
            type: "image",
            image: imageData,
          },
        ],
      },
    ],
  });

  if (assessment.flagged) {
    logger.warn("🚨 Image flagged by moderation", {
      imageUrl,
      reason: assessment.reason,
    });
  }

  return {
    flagged: assessment.flagged,
    reason: assessment.reason,
  };
}

/**
 * Utility function to convert an image URL to a base64 data URL
 * If the URL is already a base64 data URL, returns it as-is
 * Otherwise, fetches the image and converts it to base64
 */
async function ensureBase64DataUrl(
  imageUrl: string,
  logger: Logger
): Promise<string> {
  // Check if the URL is already a base64 data URL
  if (imageUrl.startsWith("data:")) {
    logger.debug("🔄 Image is already base64 data URL, using as-is");
    return imageUrl;
  }

  logger.debug("🌐 Fetching image from URL to convert to base64", { imageUrl });

  // Fetch the image and convert to base64 since OpenAI can't access localhost
  const imageResponse = await fetch(imageUrl);
  if (!imageResponse.ok) {
    throw new Error(`Failed to fetch image: ${imageResponse.status}`);
  }

  const imageBuffer = await imageResponse.arrayBuffer();
  const base64Image = Buffer.from(imageBuffer).toString("base64");

  // Determine the MIME type from the URL or response headers
  const contentType = imageResponse.headers.get("content-type") || "image/jpeg";

  return `data:${contentType};base64,${base64Image}`;
}
