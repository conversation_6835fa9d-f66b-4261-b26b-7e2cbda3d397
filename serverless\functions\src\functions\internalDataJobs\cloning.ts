import { getAuth } from "firebase-admin/auth";
import { logger } from "firebase-functions/v2";
import { z } from "zod";
import {
  ModelGenerationTaskId,
  DbCollections,
  DbModelGenerationTask,
  DbWorld,
  AssetId,
  DbAsset,
  DbWorldScore,
  UserId,
  DbUser,
} from "@nilo/firebase-schema";
import { PlainLsonObject } from "@liveblocks/node";
import {
  CollectionReference,
  FieldValue,
  QueryDocumentSnapshot,
} from "firebase-admin/firestore";
// eslint-disable-next-line no-restricted-imports -- used for getting the source storage bucket
import { getStorage } from "firebase-admin/storage";
import { getStorageBucketFromDatabaseName } from "@nilo/isolated-environments";
import { db } from "../..";
import { isolatedEnvironment } from "../../isolatedEnv";
import { collection } from "../../typedFirebase";
import { cloneRoom } from "../liveblocksInternal";
import { getFirestoreFromDatabaseId } from "./dbs";
import { DataJob } from "./types";

const isUserAnonymousCache = new Map<UserId, boolean>();
async function isUserAnonymous(userId: UserId) {
  const cached = isUserAnonymousCache.get(userId);
  if (cached !== undefined) {
    return cached;
  }
  const auth = getAuth();
  let isAnonymous = false; // default to false (just in case)
  try {
    const user = await auth.getUser(userId);
    logger.debug(`User ${userId}`, { user });
    isAnonymous = user.providerData.every(
      (provider) => provider.providerId === "anonymous"
    );
  } catch (error) {
    logger.warn(`Failed to get user ${userId}: ${error}`);
  }
  isUserAnonymousCache.set(userId, isAnonymous);
  return isAnonymous;
}

const LiveObjectSchema = <T extends z.ZodRawShape>(schema: z.ZodObject<T>) =>
  z.object({
    liveblocksType: z.literal("LiveObject"),
    data: schema,
  });
const LiveMapSchema = <T extends z.ZodRawShape>(schema: z.ZodObject<T>) =>
  z.object({
    liveblocksType: z.literal("LiveMap"),
    data: z.record(z.string(), schema),
  });

// very limited schema for storage documents, only used for extracting model generation task IDs from storage documents
const StorageDocumentSchema = LiveObjectSchema(
  z
    .object({
      entities: LiveMapSchema(
        LiveObjectSchema(
          z
            .object({
              taskId: ModelGenerationTaskId,
              originalMeshTaskId: ModelGenerationTaskId,
              modelData: z
                .object({
                  taskId: ModelGenerationTaskId.optional(),
                })
                .nullish(),
            })
            .partial()
        )
      ),
    })
    .partial()
);

export class CloneWorldsDataJob extends DataJob<"clone_worlds"> {
  private worldsCloned = 0;
  private worldsSkipped = 0;
  private taskIds = new Set<ModelGenerationTaskId>();
  private tasksCloned = 0;
  private assetIds = new Set<AssetId>();
  private assetsCloned = 0;

  private updateMessage(
    stage: "cloning_worlds" | "cloning_tasks" | "cloning_assets" | "done"
  ) {
    switch (stage) {
      case "cloning_worlds":
        return this.setMessage(
          `Cloned ${this.worldsCloned}/${this.config.worldIds.length} worlds${this.worldsSkipped > 0 ? ` (${this.worldsSkipped} skipped)` : ""}. Found ${this.taskIds.size} model generation task IDs`
        );
      case "cloning_tasks":
        return this.setMessage(
          `Cloned ${this.worldsCloned}/${this.config.worldIds.length} worlds${this.worldsSkipped > 0 ? ` (${this.worldsSkipped} skipped)` : ""}. Cloned ${this.tasksCloned}/${this.taskIds.size} model generation tasks`
        );
      case "cloning_assets":
        return this.setMessage(
          `Cloned ${this.worldsCloned}/${this.config.worldIds.length} worlds${this.worldsSkipped > 0 ? ` (${this.worldsSkipped} skipped)` : ""}. Cloned ${this.assetsCloned}/${this.assetIds.size} assets`
        );
      case "done":
        return this.setMessage(
          `Cloned: ${this.worldsCloned}/${this.config.worldIds.length} worlds${this.worldsSkipped > 0 ? ` (${this.worldsSkipped} skipped)` : ""}, ${this.tasksCloned}/${this.taskIds.size} model generation tasks, ${this.assetsCloned}/${this.assetIds.size} assets`
        );
    }
  }

  public override async run(): Promise<void> {
    this.logger.info("Running clone world job", this.config);
    const worldScoresCollection = collection<DbWorldScore>(
      DbCollections.worldScores
    );
    // FIXME: put the schema param once we have the actual schema
    const liveblocksRoomDataCollection = collection(DbCollections.roomData);

    // get source world data
    const sourceDb = getFirestoreFromDatabaseId(this.config.fromDatabase);
    const sourceWorldsCollection = sourceDb.collection(
      DbCollections.worlds
    ) as CollectionReference<DbWorld, DbWorld>;
    const sourceBucket = getStorage().bucket(
      getStorageBucketFromDatabaseName(this.config.fromDatabase)
    );
    const sourceLiveblocksRoomDataCollection = sourceDb.collection(
      DbCollections.roomData
    );

    // prepare the batch and the target bucket
    const batch = db.batch();
    const bucket = getStorage().bucket(isolatedEnvironment.bucket);

    // collect all model generation task IDs
    const extractModelGenerationTaskIds = ({
      sourceRoomId,
      targetRoomId,
      storageDocument,
    }: {
      sourceRoomId: string;
      targetRoomId: string;
      storageDocument: PlainLsonObject;
    }) => {
      const { success, data } =
        StorageDocumentSchema.safeParse(storageDocument);
      if (!success) {
        this.logger.error("Invalid storage document", {
          targetRoomId,
          sourceRoomId,
          storageDocument,
        });
        return;
      }
      // extract model generation task IDs from possible nested objects
      Object.values(data.data.entities?.data ?? {}).forEach(
        ({ data: { taskId, originalMeshTaskId, modelData } }) => {
          [taskId, originalMeshTaskId, modelData?.taskId].forEach((taskId) => {
            if (taskId) {
              this.info(`Found model generation task ID: ${taskId}`);
              this.taskIds.add(taskId);
            }
          });
        }
      );
    };

    // note: we don't batch liveblocks cloning because it takes too much memory
    for (const worldId of this.config.worldIds) {
      const sourceWorld = await sourceWorldsCollection.doc(worldId).get();
      const sourceWorldData = sourceWorld.data();
      if (!sourceWorldData) {
        this.error(
          `World not found in ${this.config.fromDatabase}: ${worldId}`
        );
        continue;
      }
      const worldRef = collection<DbWorld>(DbCollections.worlds).doc(worldId);
      const updatedAt = (await worldRef.data())?.updatedAt;
      if (
        updatedAt &&
        sourceWorldData.updatedAt.toDate() < updatedAt.toDate()
      ) {
        this.warn(
          `Target world already exists and was updated after the source world: ${worldId}. ${this.config.overwrite ? "Overwriting..." : "Skipping..."}`
        );
        if (!this.config.overwrite) {
          this.worldsSkipped++;
          continue;
        }
      }

      // clone liveblocks room
      const roomIdFrom = sourceWorldData.liveblocksRoomId;
      const roomIdTo = `${sourceWorldData.liveblocksRoomId}-${this.id}`;
      this.info(
        `Cloning liveblocks room from ${sourceWorldData.liveblocksRoomId} to ${roomIdTo}`
      );
      try {
        await cloneRoom({
          roomIdFrom,
          roomIdTo,
          inspectStorageDocument: async (storageDocument) =>
            extractModelGenerationTaskIds({
              sourceRoomId: roomIdFrom,
              targetRoomId: roomIdTo,
              storageDocument,
            }),
        });
      } catch (error) {
        this.error(`Failed to clone liveblocks room: ${error}`);
        this.worldsSkipped++;
        continue;
      }

      // clone screenshot
      try {
        this.logger.debug(
          `Cloning screenshot of ${worldId} from ${sourceBucket.name} to ${bucket.name}`
        );
        const screenshotPath = `world-previews/sd-screenshots/${worldId}.webp`;
        await sourceBucket
          .file(screenshotPath)
          .copy(bucket.file(screenshotPath));
        this.logger.debug(`Cloned screenshot of ${worldId}`);
      } catch (error) {
        this.warn(`Failed to clone screenshot of ${worldId}: ${error}`);
      }

      // clone liveblocks room data
      const sourceLiveblocksRoomData = (
        await sourceLiveblocksRoomDataCollection.doc(roomIdFrom).get()
      ).data();
      if (sourceLiveblocksRoomData) {
        // update room data (in batch)
        batch.set(
          liveblocksRoomDataCollection.doc(roomIdTo).ref,
          sourceLiveblocksRoomData
        );
      }

      // update world data (in batch)
      batch.set(worldRef.ref, {
        ...DbWorld.parse(sourceWorldData),
        liveblocksRoomId: roomIdTo,
        internalCreatedByCloning: true,
      });

      // create empty world score
      batch.set(worldScoresCollection.doc(worldId).ref, {
        updatedAt: sourceWorldData.updatedAt,
        isPublic: sourceWorldData.isPublic,
        score: 0,
        likes: 0,
        totalPlayTimeSeconds: 0,
      });

      this.worldsCloned++;
      await this.updateMessage("cloning_worlds");
    }

    // clone model generation tasks
    if (this.taskIds.size > 0) {
      this.info(
        `Found ${this.taskIds.size} model generation task IDs to clone`
      );
      const sourceModelGenerationTasksCollection = sourceDb.collection(
        DbCollections.modelGenerationTasks
      ) as CollectionReference<DbModelGenerationTask, DbModelGenerationTask>;
      for (const taskId of this.taskIds) {
        // get source model generation task data
        const sourceModelGenerationTask =
          await sourceModelGenerationTasksCollection.doc(taskId).get();
        const sourceModelGenerationTaskData = sourceModelGenerationTask.data();
        if (!sourceModelGenerationTaskData) {
          this.error(
            `Model generation task not found in ${this.config.fromDatabase}: ${taskId}`
          );
          continue;
        }

        // collect asset IDs
        if (sourceModelGenerationTaskData.assets) {
          for (const assetId of sourceModelGenerationTaskData.assets) {
            this.assetIds.add(assetId);
            this.logger.debug(`Found asset ID: ${assetId}`);
          }
        }

        // clone model generation task
        const modelGenerationTaskRef = collection<DbModelGenerationTask>(
          DbCollections.modelGenerationTasks
        ).doc(taskId);
        batch.set(modelGenerationTaskRef.ref, {
          ...DbModelGenerationTask.parse(sourceModelGenerationTaskData),
          internalCreatedByCloning: true,
        });

        // update stats
        this.tasksCloned++;
        await this.updateMessage("cloning_tasks");
      }
    } else {
      this.info(`No model generation task IDs found to clone`);
    }

    // clone required assets
    if (this.assetIds.size > 0) {
      this.info(`Found ${this.assetIds.size} asset IDs to clone`);
      const sourceAssetsCollection = sourceDb.collection(
        DbCollections.assets
      ) as CollectionReference<DbAsset, DbAsset>;
      for (const assetId of this.assetIds) {
        // get source asset data
        const sourceAsset = await sourceAssetsCollection.doc(assetId).get();
        const sourceAssetData = sourceAsset.data();
        if (!sourceAssetData) {
          this.error(`Asset not found: ${assetId}`);
          continue;
        }

        // clone asset
        const assetRef = collection<DbAsset>(DbCollections.assets).doc(assetId);
        batch.set(assetRef.ref, {
          ...DbAsset.parse(sourceAssetData),
          internalCreatedByCloning: true,
        });

        // update stats
        this.assetsCloned++;
        await this.updateMessage("cloning_assets");
      }
    } else {
      this.info(`No asset IDs found to clone`);
    }

    // FIXME: what about uthana animations?

    // FIXME: clone storage data, maybe?

    // await the batch to complete
    await batch.commit();
    await this.updateMessage("done");
  }
}

export class CloneAllWorldsDataJob extends DataJob<"clone_all_worlds"> {
  static readonly MAX_WORLDS_PER_JOB = 50;

  private _worldsSkipped = 0;
  private _worldsScheduledForCloning = 0;
  private _statusUpdates: Promise<unknown>[] = [];

  private get worldsSkipped() {
    return this._worldsSkipped;
  }
  private set worldsSkipped(value: number) {
    this._worldsSkipped = value;
    if (this.worldsProcessed % 100 === 0) {
      this._statusUpdates.push(this.updateMessage());
    }
  }

  private set worldsScheduledForCloning(value: number) {
    this._worldsScheduledForCloning = value;
    if (this.worldsProcessed % 100 === 0) {
      this._statusUpdates.push(this.updateMessage());
    }
  }
  private get worldsScheduledForCloning() {
    return this._worldsScheduledForCloning;
  }

  private get worldsProcessed() {
    return this._worldsScheduledForCloning + this._worldsSkipped;
  }

  private async waitForStatusUpdates() {
    await Promise.all(this._statusUpdates);
    this._statusUpdates = [];
  }

  private updateMessage() {
    const message = `Scheduled cloning of ${this.worldsScheduledForCloning} worlds${this.worldsSkipped > 0 ? ` (${this.worldsSkipped} skipped)` : ""}`;
    return Promise.all([this.info(message), this.setMessage(message)]);
  }

  public override async run(): Promise<void> {
    this.logger.info("Running clone universe job", this.config);

    // get source data
    const sourceDb = getFirestoreFromDatabaseId(this.config.fromDatabase);
    const sourceWorldsCollection = sourceDb.collection(
      DbCollections.worlds
    ) as CollectionReference<DbWorld, DbWorld>;
    const worldCount = (await sourceWorldsCollection.count().get()).data()
      .count;
    this.info(`Found ${worldCount} worlds to clone`);
    this.setMessage(`Found ${worldCount} worlds to clone`);
    let worldIdsBatch = [];
    for await (const world of sourceWorldsCollection
      .select("ownerId")
      .stream()) {
      const worldSnapshot = world as unknown as QueryDocumentSnapshot<
        DbWorld,
        DbWorld
      >;
      if (
        this.config.skipOwnedByAnonymousUsers &&
        (await isUserAnonymous(worldSnapshot.data().ownerId))
      ) {
        this.logger.info(
          `Skipping world owned by anonymous user: ${worldSnapshot.id}`
        );
        this.worldsSkipped++;
        continue;
      }
      this.logger.debug(`Cloning world ${worldSnapshot.id}`, { world });
      worldIdsBatch.push(worldSnapshot.id);
      this.worldsScheduledForCloning++;
      if (worldIdsBatch.length >= CloneAllWorldsDataJob.MAX_WORLDS_PER_JOB) {
        this.scheduleSubJob({
          type: "clone_worlds",
          worldIds: worldIdsBatch,
          fromDatabase: this.config.fromDatabase,
          overwrite: this.config.overwrite,
        });
        worldIdsBatch = [];
      }
    }
    if (worldIdsBatch.length > 0) {
      this.scheduleSubJob({
        type: "clone_worlds",
        worldIds: worldIdsBatch,
        fromDatabase: this.config.fromDatabase,
        overwrite: this.config.overwrite,
      });
    }
    await this.waitForStatusUpdates();
    await this.updateMessage();
  }
}

export class CloneAllUsersDataJob extends DataJob<"clone_all_users"> {
  private _usersCloned = 0;
  private _usersSkipped = 0;
  private _statusUpdates: Promise<unknown>[] = [];

  private get usersCloned() {
    return this._usersCloned;
  }
  private get usersSkipped() {
    return this._usersSkipped;
  }
  private get usersProcessed() {
    return this._usersCloned + this._usersSkipped;
  }

  private set usersCloned(value: number) {
    this._usersCloned = value;
    if (this.usersProcessed % 100 === 0) {
      this._statusUpdates.push(this.updateMessage());
    }
  }
  private set usersSkipped(value: number) {
    this._usersSkipped = value;
    if (this.usersProcessed % 100 === 0) {
      this._statusUpdates.push(this.updateMessage());
    }
  }

  private async waitForStatusUpdates() {
    await Promise.all(this._statusUpdates);
    this._statusUpdates = [];
  }

  private updateMessage() {
    const message = `Cloned ${this.usersCloned} users${this.usersSkipped > 0 ? ` (${this.usersSkipped} skipped)` : ""}`;
    return Promise.all([this.info(message), this.setMessage(message)]);
  }

  public override async run(): Promise<void> {
    this.logger.info("Running clone all users job", this.config);
    const sourceDb = getFirestoreFromDatabaseId(this.config.fromDatabase);
    const sourceUsersCollection = sourceDb.collection(
      DbCollections.users
    ) as CollectionReference<DbUser, DbUser>;
    const userCount = (await sourceUsersCollection.count().get()).data().count;
    this.info(`Found ${userCount} users to clone`);
    this.setMessage(`Found ${userCount} users to clone`);

    const usersCollection = collection<DbUser>(DbCollections.users);
    let batch = db.batch();
    let batchSize = 0;
    // TODO: process in batches
    for await (const user of sourceUsersCollection.stream()) {
      const sourceUserSnapshot = user as unknown as QueryDocumentSnapshot<
        DbUser,
        DbUser
      >;
      const userId = sourceUserSnapshot.id;

      // skip anonymous users
      if (this.config.skipAnonymousUsers && (await isUserAnonymous(userId))) {
        this.logger.info(`Skipping anonymous user: ${userId}`);
        this.usersSkipped++;
        continue;
      }

      // skip users that were updated after the source user
      const sourceUpdatedAt = sourceUserSnapshot.data().updatedAt;
      const userRef = usersCollection.doc(userId);
      const userData = await userRef.data();
      const updatedAt = userData?.updatedAt;
      if (
        sourceUpdatedAt &&
        updatedAt &&
        sourceUpdatedAt.toDate() < updatedAt.toDate()
      ) {
        this.info(
          `Skipping user: ${userId} because it was updated after the source user`
        );
        this.usersSkipped++;
        continue;
      }

      // clone user
      this.logger.debug(`Cloning user ${userId}`, {
        user: sourceUserSnapshot.data(),
      });
      batch.set(userRef.ref, {
        ...DbUser.parse(sourceUserSnapshot.data()),
        updatedAt: FieldValue.serverTimestamp(),
        internalCreatedByCloning: true,
      });
      batchSize++;
      if (batchSize >= 50) {
        await batch.commit();
        batch = db.batch();
        batchSize = 0;
      }
      this.usersCloned++;
    }
    if (batchSize > 0) {
      await batch.commit();
    }
    await this.waitForStatusUpdates();
    await this.updateMessage();
  }
}
