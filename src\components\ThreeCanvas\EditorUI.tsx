import { AnimatePresence, motion, MotionProps } from "framer-motion";
import { ChevronLeftIcon } from "lucide-react";
import { useEffect, useState, type ReactNode } from "react";

import ThreejsSceneHierarchyView from "@/components/debugging/ThreejsSceneHierarchyView";
import { FeedbackModal } from "@/components/editor/help/FeedbackModal";
import { KeyboardShortcutsModal } from "@/components/editor/help/KeyboardShortcutsModal";
import { DevSettingsPanel } from "@/components/modals/DevSettingsPanel";
import { EntitiesPanel } from "@/components/modals/EntitiesPanel";
import { EnvironmentSettingsPanel } from "@/components/modals/EnvironmentSettingsPanel";
import { InspectorPanel } from "@/components/modals/InspectorPanel";
import { EditorSidebar } from "@/components/ThreeCanvas/EditorSidebar";
import { WorldSettings as LegacyWorldSettings } from "@/components/WorldSettings";
import {
  useEditorUIActions,
  useEditorUIState,
} from "@/contexts/EditorUIContext";
import { Client } from "@/core/client";
import { useMySelectedEntities } from "@/hooks/useMySelectedEntities";
import { getFeatureFlag } from "@/utils/feature-flags";

export function EditorUI() {
  const appIsReady = useIsAppReady();
  const { selectedEntities } = useMySelectedEntities();
  const isMouseInLeftArea = useSpecialMouseEnterLeaveArea();

  const {
    isEnvironmentSettingsOpen,
    isDevSettingsOpen,
    isLegacyWorldSettingsOpen,
    isSceneHierarchyOpen,
    isEntitiesOpen,
    isInspectorOpen,
    isInspectorEnabled,
    currentLeftPanel,
    isLeftPanelPinned,
  } = useEditorUIState();

  const {
    toggleLeftPanel,
    toggleInspectorPanel,
    toggleInspectorEnabled,
    setCurrentLeftPanel,
  } = useEditorUIActions();

  // Auto-show inspector when entities are selected, auto-hide when none selected
  useEffect(() => {
    // Only show inspector if feature flag is enabled
    if (!getFeatureFlag("builderUiInspector")) {
      return;
    }

    if (selectedEntities.length > 0 && !isInspectorOpen) {
      toggleInspectorPanel(true);
    } else if (selectedEntities.length === 0 && isInspectorOpen) {
      toggleInspectorPanel(false);
    }
  }, [selectedEntities.length, isInspectorOpen, toggleInspectorPanel]);

  // Auto-close left panel when mouse leaves left area (if not pinned)
  useEffect(() => {
    if (!isMouseInLeftArea && currentLeftPanel && !isLeftPanelPinned) {
      setCurrentLeftPanel(null);
    }
  }, [
    isMouseInLeftArea,
    currentLeftPanel,
    isLeftPanelPinned,
    setCurrentLeftPanel,
  ]);

  if (!appIsReady) {
    return null;
  }

  return (
    <>
      {getFeatureFlag("builderUiSidebar") && (
        <EditorSidebar
          currentPanel={currentLeftPanel}
          openPanel={(p) => toggleLeftPanel(p, true)}
        />
      )}

      <div
        id="editor-ui"
        className="fixed inset-[1rem] left-[4rem] top-[4.5rem] select-none pointer-events-none"
      >
        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isEnvironmentSettingsOpen}
        >
          <EnvironmentSettingsPanel />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isDevSettingsOpen}
        >
          <DevSettingsPanel />
        </ScreenDockedPanel>

        {/* Legacy stuff // Only useful for debugging */}

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isLegacyWorldSettingsOpen}
        >
          <LegacyWorldSettings />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isEntitiesOpen}
        >
          <EntitiesPanel />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="top"
          isOpen={isSceneHierarchyOpen}
        >
          <ThreejsSceneHierarchyView />
        </ScreenDockedPanel>

        {/* Legacy stuff [end] */}

        {/* Right side panels */}
        <ScreenDockedPanel
          alignmentHorizontal="right"
          alignmentVertical="top"
          isOpen={
            isInspectorEnabled &&
            isInspectorOpen &&
            getFeatureFlag("builderUiInspector")
          }
        >
          <InspectorPanel selectedEntities={selectedEntities} />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="right"
          alignmentVertical="center"
          isOpen={
            !isInspectorEnabled &&
            isInspectorOpen &&
            getFeatureFlag("builderUiInspector")
          }
          slideDistance={4}
        >
          <button
            onClick={() => toggleInspectorEnabled(true)}
            className="flex items-center justify-center w-12 h-12 transition-all duration-200 pointer-events-auto"
            title="Re-enable Inspector"
          >
            <ChevronLeftIcon className="h-8 w-8 text-nilo-text-secondary/60 hover:text-nilo-text-secondary stroke-2" />
          </button>
        </ScreenDockedPanel>

        <KeyboardShortcutsModal />
        <FeedbackModal />
      </div>
    </>
  );
}

function ScreenDockedPanel({
  children,
  alignmentHorizontal,
  alignmentVertical = "center",
  isOpen,
  slideDistance = 400,
  kkey: key,
}: {
  children: ReactNode;
  alignmentHorizontal: "left" | "right";
  alignmentVertical: "top" | "bottom" | "center";
  isOpen: boolean;
  slideDistance?: number;
  kkey?: string;
}) {
  const getHorizontalAnimation = (): MotionProps => {
    const transitionIn = {
      type: "spring",
      stiffness: 300,
      damping: 8,
      mass: 0.4,
    } as const;

    const transitionOut = {
      duration: 0.2,
      ease: "easeInOut",
    } as const;

    if (alignmentHorizontal === "left") {
      return {
        initial: {
          x: -slideDistance,
          opacity: 0,
          transition: transitionIn,
        },
        exit: {
          x: -slideDistance,
          opacity: 0,
          transition: transitionOut,
        },
      };
    } else {
      return {
        initial: {
          x: slideDistance,
          opacity: 0,
          transition: transitionIn,
        },
        exit: {
          x: slideDistance,
          opacity: 0,
          transition: transitionOut,
        },
      };
    }
  };

  const getVerticalAlignment = () => {
    switch (alignmentVertical) {
      case "top":
        return "justify-start";
      case "bottom":
        return "justify-end";
      case "center":
      default:
        return "justify-center";
    }
  };

  const getHorizontalAlignment = () => {
    return alignmentHorizontal === "left" ? "items-start" : "items-end";
  };

  const animationProps = getHorizontalAnimation();

  return (
    <AnimatePresence mode="sync">
      {isOpen && (
        <motion.div
          {...animationProps}
          animate={{ x: 0, opacity: 1 }}
          className={`absolute inset-0 flex flex-col ${getVerticalAlignment()} ${getHorizontalAlignment()}`}
          key={key}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

/**
 * Hook that tracks mouse position relative to an invisible HTML element
 * that spans the width of the left panels for accurate mouse tracking.
 *
 * @returns boolean indicating if mouse is over the left panel area
 *
 * //// TODO: Port to tailwind and react, e.g. <SpecialMouseEnterLeaveArea onLeave={...} />
 */
function useSpecialMouseEnterLeaveArea(): boolean {
  const [isInLeftArea, setIsInLeftArea] = useState(false);

  useEffect(() => {
    // Create invisible element for mouse tracking
    const trackingElement = document.createElement("div");
    trackingElement.id = "mouse-tracking-area";
    trackingElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 520px;
      height: 100vh;
      pointer-events: none;
      z-index: 99999;
      opacity: 0.0;
    `;

    document.body.appendChild(trackingElement);

    const handleMouseMove = (event: MouseEvent) => {
      const rect = trackingElement.getBoundingClientRect();
      const isInLeft =
        event.clientX >= rect.left &&
        event.clientX <= rect.right &&
        event.clientY >= rect.top &&
        event.clientY <= rect.bottom;

      setIsInLeftArea(isInLeft);
    };

    // Add event listener
    document.addEventListener("mousemove", handleMouseMove);

    // Cleanup
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      if (trackingElement.parentNode) {
        trackingElement.parentNode.removeChild(trackingElement);
      }
    };
  }, []);

  return isInLeftArea;
}

function useIsAppReady() {
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    const onInit = () => {
      setAppIsReady(true);
    };

    Client.events.on("initialized", onInit);

    return () => {
      Client.events.off("initialized", onInit);
    };
  }, []);

  return appIsReady;
}
