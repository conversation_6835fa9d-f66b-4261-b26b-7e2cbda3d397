import { ByteStreamWriter } from "@evenstar/byteform";
import { encode as encodeCBOR } from "cbor-x";
import { GameEntityComponent } from "../components";
import { NetworkEntity } from "../entity";
import { NetworkClientService } from "./NetworkClientService";
import { DeserializeService } from "./DeserializeService";
import {
  EntityData,
  Query,
  System,
  SystemContext,
  SystemRunner,
  World,
} from "@nilo/ecs";
import { MessageSchema } from "@nilo/network";
import {
  EntitySerializationState,
  ComponentSerializeSystem,
  EntitySerializationComponent,
} from "@nilo/ecs-networking";

/** Returns a system runner for all networking systems. */
export function makeNetworkSystemRunner(): SystemRunner {
  return new SystemRunner([
    new SerializeGameEntitySystem(),
    new ComponentSerializeSystem(),
    new TransmitEntitySystem(),
    new SendPingSystem(),
  ]);
}

/**
 * Manually serializes GameEntityComponents containing NetworkComponents into
 * EntitySerializationState so that we don't have to duplicate a bunch of code
 * and share state with TransmitEntitySystem. Yes, it IS hacky. C'est la vie.
 */
export class SerializeGameEntitySystem implements System {
  name = "SerializeGameEntitySystem";
  entityFilter = [GameEntityComponent];

  private _gameEntityQuery = new Query([GameEntityComponent]);
  private _writeBuf: ByteStreamWriter = new ByteStreamWriter(256, {
    maxByteLength: 64 * 1024, // 64KiB *should* be plenty
  });

  run(world: World, _ctx: SystemContext): void {
    this._gameEntityQuery.forEach(world, (entity, [gameEntity]) => {
      // only update NetworkEntity instances
      if (!(gameEntity instanceof NetworkEntity)) {
        return;
      }

      // skip entities that are not network-dirty
      if (!gameEntity.netShouldUpdate) {
        return;
      } else {
        gameEntity.netShouldUpdate = false;
      }

      // serialize the network entity's data
      this._writeBuf.reset();
      this._writeBuf.writeUint32(gameEntity.UNIQUE_ID);
      const entityData = gameEntity.serialize();
      if (gameEntity.SCHEMA) {
        this._writeBuf.writeSchema(gameEntity.SCHEMA, entityData);
      } else {
        // Use CBOR serialization when no schema is provided
        const cborData = encodeCBOR(entityData);
        this._writeBuf.writeBytes(cborData);
      }

      // finish serializing the network message
      const payload = this._writeBuf.commit();

      // ensure entity has a serialized state
      // alternative is to use onEntityAdded(), but that will skip entities
      // already in the world when this system is added
      let serialization = entity.getComponent(EntitySerializationComponent);
      if (!serialization) {
        serialization = new EntitySerializationState();
        entity.addComponent(EntitySerializationComponent, serialization);
      }

      // update the serialized entity state
      if (serialization.setComponent(GameEntityComponent.KEY, payload)) {
        // minimally track dirtiness
        entity.markDirty(EntitySerializationComponent);
      }
    });
  }

  onEntityAdded(world: World, entity: EntityData, _ctx: SystemContext): void {
    // ensure that all network entities have serialized state to be sync'd
    // run() will actually populate the game entity's serialized component
    const gameEntity = entity.getComponent(GameEntityComponent);
    if (gameEntity instanceof NetworkEntity) {
      if (!entity.hasComponent(EntitySerializationComponent)) {
        const state = new EntitySerializationState();
        entity.addComponent(EntitySerializationComponent, state);
      }

      // assert that the callbacks for this entity actually exist
      const callbacks = world
        .getService(DeserializeService)
        ?.getEntityCallbacks(gameEntity.UNIQUE_ID);

      if (!callbacks) {
        throw new Error(
          `${gameEntity.constructor.name} is not registered with DeserializeService`
        );
      }
    }
  }

  onEntityRemoved(
    _world: World,
    entity: EntityData,
    _ctx: SystemContext
  ): void {
    // remove the network entity component from the serialized state
    // technically also removes non-NetworkEntity entries
    const serialization = entity.getComponent(EntitySerializationComponent);
    if (serialization?.removeComponent(GameEntityComponent.KEY)) {
      // minimally track dirtiness
      entity.markDirty(EntitySerializationComponent);
    }
  }
}

/**
 * Sends serialized entity update messages to the server based
 * on changes to EntitySerializationData.
 */
export class TransmitEntitySystem implements System {
  prefix = "📡";
  name = "TransmitEntitySystem";
  entityFilter = [EntitySerializationComponent];

  private _serializedQuery = new Query([EntitySerializationComponent]);

  run(world: World, _ctx: SystemContext): void {
    // get network client service
    const client = world.getService(NetworkClientService);
    if (!client) {
      return;
    }

    this._serializedQuery.forEach(world, (entity, [serializationData]) => {
      for (const [componentKey, component] of serializationData.components) {
        // only sync components marked as dirty
        if (!component.dirty) {
          continue;
        }

        // flag as not dirty
        component.dirty = false;

        // sync the component's data
        if (component.data) {
          client.send(MessageSchema.InsertComponent, {
            entity: entity.id(),
            component: componentKey,
            data: component.data,
          });
        } else {
          client.send(MessageSchema.RemoveComponent, {
            entity: entity.id(),
            component: componentKey,
          });
        }
      }
    });
  }

  onEntityAdded(world: World, entity: EntityData, _ctx: SystemContext): void {
    world
      .getService(NetworkClientService)
      ?.send(MessageSchema.SpawnEntity, entity.id());
  }

  onEntityRemoved(world: World, entity: EntityData, _ctx: SystemContext): void {
    world
      .getService(NetworkClientService)
      ?.send(MessageSchema.KillEntity, entity.id());
  }
}

/** System that pings the NetworkClientService at semi-regular intervals. */
export class SendPingSystem implements System {
  name = "SendPingSystem";

  private _sinceLastPing: number = 0.0;
  private _pingInterval: number = 1.0; // ping once a second

  run(world: World, ctx: SystemContext): void {
    // increment ping timer
    this._sinceLastPing += ctx.deltaTime / 1000.0; // deltaTime is in ms

    // if ping has exceeded interval, send ping and reset
    if (this._sinceLastPing > this._pingInterval) {
      world.getService(NetworkClientService)?.sendPing();
      this._sinceLastPing = this._sinceLastPing % this._pingInterval;
    }
  }
}
