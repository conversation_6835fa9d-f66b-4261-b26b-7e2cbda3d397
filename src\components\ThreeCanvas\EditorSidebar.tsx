import {
  Cog6ToothIcon,
  GlobeAmericasIcon,
  ListBulletIcon,
} from "@heroicons/react/24/solid";

import { Button } from "@/components/ui/button";
import { LeftPanelType } from "@/contexts/EditorUIContext";

const ENABLE_HOVER_OPEN = true;

//// TODO: hover out and panel pinning

export function EditorSidebar({
  currentPanel,
  openPanel,
}: {
  currentPanel: LeftPanelType | null;
  openPanel: (panel: LeftPanelType) => void;
}) {
  const panelButtons = [
    {
      id: LeftPanelType.Entities,
      icon: ListBulletIcon,
      label: "Entities",
      isActive: currentPanel === LeftPanelType.Entities,
    },
    {
      id: LeftPanelType.EnvironmentSettings,
      icon: GlobeAmericasIcon,
      label: "Environment Settings",
      isActive: currentPanel === LeftPanelType.EnvironmentSettings,
    },
    {
      id: LeftPanelType.DevSettings,
      icon: Cog6ToothIcon,
      label: "Dev Settings",
      isActive: currentPanel === LeftPanelType.DevSettings,
    },
  ];

  const handleHover = (panel: LeftPanelType) => {
    if (ENABLE_HOVER_OPEN) {
      openPanel(panel);
    }
  };

  const handleClick = (panel: LeftPanelType) => {
    openPanel(panel);
  };

  return (
    <div className="fixed left-[1rem] top-[4.5rem] h-full pointer-events-auto z-50">
      <div className="bg-gray-800/25 rounded-full flex flex-col items-center space-y-1">
        {panelButtons.map(({ id, icon: Icon, label, isActive }) => (
          <Button
            key={id}
            onClick={() => handleClick(id)}
            onMouseEnter={() => handleHover(id)}
            variant="ghost"
            size="icon"
            className={`rounded-full focus-visible:ring-0 hover:ring-transparent ${isActive ? "bg-nilo-fill-secondary/60" : "bg-transparent"}`}
            title={label}
          >
            <Icon />
          </Button>
        ))}
      </div>
    </div>
  );
}
