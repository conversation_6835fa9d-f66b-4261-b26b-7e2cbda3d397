import { signInWithCustomToken } from "firebase/auth";
import { auth } from "@/config/firebase";
import { getFunctionUrl } from "@nilo/isolated-environments";

export const DISCORD_CONFIG = {
  clientId: process.env.DISCORD_CLIENT_ID || "1300760051782520843",
  redirectUri: `${window.location.origin}/auth/discord/callback`,
  scopes: ["identify", "email"],
  apiUrl:
    process.env.DISCORD_API_URL ||
    getFunctionUrl("main", "authenticateDiscord"),
};

export const getDiscordAuthUrl = () => {
  const params = new URLSearchParams({
    client_id: DISCORD_CONFIG.clientId,
    redirect_uri: DISCORD_CONFIG.redirectUri,
    response_type: "code",
    scope: DISCORD_CONFIG.scopes.join(" "),
    state: crypto.randomUUID(),
  });

  return `https://discord.com/oauth2/authorize?${params.toString()}`;
};

export const exchangeDiscordCode = async (code: string) => {
  if (!DISCORD_CONFIG.apiUrl) {
    throw new Error("DISCORD_API_URL is not set");
  }

  const response = await fetch(DISCORD_CONFIG.apiUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      code,
      redirectUri: DISCORD_CONFIG.redirectUri,
    }),
  });

  if (!response.ok) {
    console.error("Discord authentication failed", response);
    const error = await response.json();
    throw new Error(error.message || "Discord authentication failed");
  }

  return response.json();
};

export const signInWithDiscordCode = async (code: string) => {
  const { customToken } = await exchangeDiscordCode(code);
  return signInWithCustomToken(auth, customToken);
};
